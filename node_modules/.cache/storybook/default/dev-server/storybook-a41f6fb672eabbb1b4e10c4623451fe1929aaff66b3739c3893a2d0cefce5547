{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "Lm9Tyy6z-HCtqu_Q4COvY", "sessionId": "JP2bu1b_CBsa_ahA1fI_f", "payload": {"eventType": "dev"}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.12.2", "storybookVersion": "9.0.18", "cliVersion": "9.0.18"}}, "timestamp": 1753778851327}, "init-step": {"body": {"eventType": "init-step", "eventId": "GL-YCwHfxOnkJhhGWWKjf", "sessionId": "JP2bu1b_CBsa_ahA1fI_f", "metadata": {"generatedAt": 1753777382666, "userSince": 1752833122104, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "storybookVersionSpecifier": "9.0.18", "language": "typescript"}, "payload": {"step": "new-user-check", "newUser": true}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.12.2", "storybookVersion": "9.0.18", "cliVersion": "9.0.18", "anonymousId": "6264b0e86815756e6cbe0b68559529c3b205d7188f87b55f972186c7f26e89f4"}}, "timestamp": 1753777383613}, "init": {"body": {"eventType": "init", "eventId": "PFWfCuhqfymZIWXbJsE4q", "sessionId": "JP2bu1b_CBsa_ahA1fI_f", "metadata": {"generatedAt": 1753777382666, "userSince": 1752833122104, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "storybookVersionSpecifier": "9.0.18", "language": "typescript"}, "payload": {"projectType": "REACT", "features": {"dev": true, "docs": true, "test": true, "onboarding": true}, "newUser": true}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.12.2", "storybookVersion": "9.0.18", "cliVersion": "9.0.18", "anonymousId": "6264b0e86815756e6cbe0b68559529c3b205d7188f87b55f972186c7f26e89f4"}}, "timestamp": 1753777634162}, "dev": {"body": {"eventType": "dev", "eventId": "vhTd4X8Xy9kVRw9JFWlze", "sessionId": "JP2bu1b_CBsa_ahA1fI_f", "metadata": {"generatedAt": 1753778854013, "userSince": 1752833122104, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.4.4"}, "testPackages": {"@chromatic-com/storybook": "4.0.1", "@storybook/addon-vitest": "9.0.18", "@testing-library/jest-dom": "6.6.4", "@testing-library/react": "16.3.0", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "playwright": "1.54.1", "vitest": "3.2.4"}, "hasRouterPackage": true, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.18", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.0.18"}, "storybook": {"version": "9.0.18"}}, "addons": {"@chromatic-com/storybook": {"version": "4.0.1"}, "@storybook/addon-docs": {"version": "9.0.18"}, "@storybook/addon-onboarding": {"version": "9.0.18"}, "@storybook/addon-a11y": {"version": "9.0.18"}, "@storybook/addon-vitest": {"version": "9.0.18"}}}, "payload": {"versionStatus": "success", "storyIndex": {"storyCount": 10, "componentCount": 2, "pageStoryCount": 0, "playStoryCount": 1, "autodocsCount": 2, "mdxCount": 0, "exampleStoryCount": 8, "exampleDocsCount": 3, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "svelteCsfV4Count": 0, "svelteCsfV5Count": 0, "version": 5}, "storyStats": {"factory": 0, "play": 1, "render": 1, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 10, "storyFn": 0, "mount": 0, "moduleMock": 0}}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.12.2", "storybookVersion": "9.0.18", "cliVersion": "9.0.18", "anonymousId": "6264b0e86815756e6cbe0b68559529c3b205d7188f87b55f972186c7f26e89f4"}}, "timestamp": 1753778854863}, "addon-onboarding": {"body": {"eventType": "addon-onboarding", "eventId": "kOHXFeZXpq5MU0E1MPiMp", "sessionId": "JP2bu1b_CBsa_ahA1fI_f", "metadata": {"generatedAt": 1753777744991, "userSince": 1752833122104, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.0.1", "@storybook/addon-vitest": "9.0.18", "vitest": "3.2.4", "@vitest/browser": "3.2.4", "playwright": "1.54.1", "@vitest/coverage-v8": "3.2.4"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.18", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.0.18"}, "storybook": {"version": "9.0.18"}}, "addons": {"@chromatic-com/storybook": {"version": "4.0.1"}, "@storybook/addon-docs": {"version": "9.0.18"}, "@storybook/addon-onboarding": {"version": "9.0.18"}, "@storybook/addon-a11y": {"version": "9.0.18"}, "@storybook/addon-vitest": {"version": "9.0.18"}}}, "payload": {"step": "2:Controls", "addonVersion": "9.0.18"}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.12.2", "storybookVersion": "9.0.18", "cliVersion": "9.0.18", "anonymousId": "6264b0e86815756e6cbe0b68559529c3b205d7188f87b55f972186c7f26e89f4"}}, "timestamp": 1753777818084}}}