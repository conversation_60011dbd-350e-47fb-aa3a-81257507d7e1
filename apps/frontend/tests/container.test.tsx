// 容器组件测试文件
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import MainContainer from '../../../frontend/app/main_container';
import ComponentButton from '../../../frontend/componets/componet/componetButton';
import { useStore } from '../../../frontend/Store/store';

describe('容器组件测试', () => {
  it('主容器应该正确渲染', () => {
    render(<MainContainer />);
    // 检查主容器是否存在
    const container = screen.getByRole('generic');
    expect(container).toBeDefined();
  });

  it('按键组件应该正确渲染', () => {
    render(<ComponentButton />);
    
    // 检查模式按键
    const modeButton = screen.getByText('模式');
    expect(modeButton).toBeDefined();
    
    // 检查业务按键
    const businessButton = screen.getByText('业务');
    expect(businessButton).toBeDefined();
  });

  it('按键切换功能应该正常工作', () => {
    const TestComponent = () => {
      const { activeButton, setActiveButton } = useStore();
      return (
        <div>
          <div data-testid="active-button">{activeButton}</div>
          <button onClick={() => setActiveButton('business')}>切换到业务</button>
        </div>
      );
    };

    render(<TestComponent />);
    
    // 初始状态应该是模式
    expect(screen.getByTestId('active-button')).toHaveTextContent('mode');
    
    // 点击切换按键
    fireEvent.click(screen.getByText('切换到业务'));
    
    // 状态应该变为业务
    expect(screen.getByTestId('active-button')).toHaveTextContent('business');
  });
});
