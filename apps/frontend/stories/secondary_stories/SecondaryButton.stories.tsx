import type { Meta, StoryObj } from '@storybook/react';
import { SecondaryButton } from '../../../../frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton';
import type { SecondaryButtonProps } from '../../../../frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton';

const meta: Meta<typeof SecondaryButton> = {
  title: 'Components/SecondaryButton',
  component: SecondaryButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: '普通按键组件，支持持续状态模式和瞬时状态模式',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    mode: {
      control: { type: 'select' },
      options: ['toggle', 'instant'],
      description: '按键模式：toggle为持续状态模式，instant为瞬时状态模式',
    },
    isActive: {
      control: { type: 'boolean' },
      description: '初始激活状态',
    },
    text: {
      control: { type: 'text' },
      description: '按键显示文本',
    },
    disabled: {
      control: { type: 'boolean' },
      description: '是否禁用按键',
    },
    onClick: {
      action: 'clicked',
      description: '点击事件回调',
    },
    onStateChange: {
      action: 'state-changed',
      description: '状态变化回调',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 默认故事
export const Default: Story = {
  args: {
    text: '按键',
    mode: 'toggle',
    isActive: false,
    disabled: false,
  },
};

// 持续状态模式 - 未激活
export const ToggleModeInactive: Story = {
  args: {
    text: '持续模式',
    mode: 'toggle',
    isActive: false,
    disabled: false,
  },
};

// 持续状态模式 - 激活
export const ToggleModeActive: Story = {
  args: {
    text: '持续模式',
    mode: 'toggle',
    isActive: true,
    disabled: false,
  },
};

// 瞬时状态模式
export const InstantMode: Story = {
  args: {
    text: '瞬时模式',
    mode: 'instant',
    isActive: false,
    disabled: false,
  },
};

// 禁用状态
export const Disabled: Story = {
  args: {
    text: '禁用按键',
    mode: 'toggle',
    isActive: false,
    disabled: true,
  },
};

// 自定义文本
export const CustomText: Story = {
  args: {
    text: '自定义文本',
    mode: 'toggle',
    isActive: false,
    disabled: false,
  },
};

// 自定义样式
export const CustomStyles: Story = {
  args: {
    text: '自定义样式',
    mode: 'toggle',
    isActive: false,
    disabled: false,
    customStyles: {
      width: '300px',
      height: '60px',
      borderRadius: '10px',
      fontSize: '24px',
    },
  },
};

// 交互演示
export const Interactive: Story = {
  args: {
    text: '点击测试',
    mode: 'toggle',
    isActive: false,
    disabled: false,
  },
  render: (args) => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px', alignItems: 'center' }}>
        <h3>持续状态模式</h3>
        <SecondaryButton 
          {...args} 
          mode="toggle"
          text="持续模式按键"
          onStateChange={(state) => console.log('Toggle state:', state)}
        />
        
        <h3>瞬时状态模式</h3>
        <SecondaryButton 
          {...args} 
          mode="instant"
          text="瞬时模式按键"
          onStateChange={(state) => console.log('Instant state:', state)}
        />
      </div>
    );
  },
};
