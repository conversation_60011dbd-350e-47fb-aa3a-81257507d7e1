/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _frontend_app_main_container__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../frontend/app/main_container */ \"(app-pages-browser)/./frontend/app/main_container.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_frontend_app_main_container__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/app/page.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRTBEO0FBRTNDLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCxvRUFBYUE7Ozs7O0FBQ3ZCO0tBRndCQyIsInNvdXJjZXMiOlsiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L2FwcC9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IE1haW5Db250YWluZXIgZnJvbSAnLi4vZnJvbnRlbmQvYXBwL21haW5fY29udGFpbmVyJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gPE1haW5Db250YWluZXIgLz5cbn1cbiJdLCJuYW1lcyI6WyJNYWluQ29udGFpbmVyIiwiSG9tZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx":
/*!****************************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../style/style_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* harmony import */ var _event_event_secondary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../event/event_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n// 普通按键组件\nconst SecondaryButton = (param)=>{\n    let { mode = 'toggle', isActive = false, text = '按键', onClick, onStateChange, customStyles = {}, disabled = false } = param;\n    _s();\n    const [eventManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"SecondaryButton.useState\": ()=>new _event_event_secondary__WEBPACK_IMPORTED_MODULE_3__.SecondaryButtonEventManager(mode, isActive)\n    }[\"SecondaryButton.useState\"]);\n    const [currentState, setCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(isActive);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPressed, setIsPressed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 更新事件管理器的模式和状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SecondaryButton.useEffect\": ()=>{\n            eventManager.setMode(mode);\n            eventManager.setState(isActive);\n            setCurrentState(isActive);\n        }\n    }[\"SecondaryButton.useEffect\"], [\n        mode,\n        isActive,\n        eventManager\n    ]);\n    // 获取当前样式\n    const getCurrentStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[getCurrentStyles]\": ()=>{\n            const baseStyles = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.default.button;\n            let dynamicStyles = {};\n            if (mode === 'toggle') {\n                if (currentState) {\n                    // 激活状态\n                    dynamicStyles.backgroundColor = isHovered ? _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.hover.backgroundColor : _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.backgroundColor;\n                } else {\n                    // 未激活状态\n                    dynamicStyles.backgroundColor = isHovered ? _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.hover.backgroundColor : _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.backgroundColor;\n                }\n            } else if (mode === 'instant') {\n                if (isPressed) {\n                    // 按下状态\n                    dynamicStyles.backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.active.backgroundColor;\n                } else if (isHovered) {\n                    // 悬停状态\n                    dynamicStyles.backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.hover.backgroundColor;\n                } else {\n                    // 默认状态\n                    dynamicStyles.backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.default.backgroundColor;\n                }\n            }\n            return {\n                ...baseStyles,\n                ...dynamicStyles,\n                ...customStyles,\n                opacity: disabled ? 0.6 : 1,\n                cursor: disabled ? 'not-allowed' : baseStyles.cursor\n            };\n        }\n    }[\"SecondaryButton.useCallback[getCurrentStyles]\"], [\n        mode,\n        currentState,\n        isHovered,\n        isPressed,\n        customStyles,\n        disabled\n    ]);\n    // 获取文本样式\n    const getTextStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[getTextStyles]\": ()=>{\n            return _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.default.text;\n        }\n    }[\"SecondaryButton.useCallback[getTextStyles]\"], []);\n    // 处理点击事件\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleClick]\": ()=>{\n            if (disabled) return;\n            const newState = eventManager.handleClick();\n            setCurrentState(newState);\n            onClick === null || onClick === void 0 ? void 0 : onClick(newState);\n            onStateChange === null || onStateChange === void 0 ? void 0 : onStateChange(newState);\n        }\n    }[\"SecondaryButton.useCallback[handleClick]\"], [\n        disabled,\n        eventManager,\n        onClick,\n        onStateChange\n    ]);\n    // 处理鼠标按下事件\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseDown]\": ()=>{\n            if (disabled) return;\n            const newState = eventManager.handleMouseDown();\n            if (mode === 'instant') {\n                setIsPressed(true);\n                setCurrentState(newState);\n                onStateChange === null || onStateChange === void 0 ? void 0 : onStateChange(newState);\n            }\n        }\n    }[\"SecondaryButton.useCallback[handleMouseDown]\"], [\n        disabled,\n        eventManager,\n        mode,\n        onStateChange\n    ]);\n    // 处理鼠标松开事件\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseUp]\": ()=>{\n            if (disabled) return;\n            const newState = eventManager.handleMouseUp();\n            if (mode === 'instant') {\n                setIsPressed(false);\n                setCurrentState(newState);\n                onStateChange === null || onStateChange === void 0 ? void 0 : onStateChange(newState);\n            }\n        }\n    }[\"SecondaryButton.useCallback[handleMouseUp]\"], [\n        disabled,\n        eventManager,\n        mode,\n        onStateChange\n    ]);\n    // 处理鼠标进入事件\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseEnter]\": ()=>{\n            if (disabled) return;\n            setIsHovered(true);\n        }\n    }[\"SecondaryButton.useCallback[handleMouseEnter]\"], [\n        disabled\n    ]);\n    // 处理鼠标离开事件\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseLeave]\": ()=>{\n            if (disabled) return;\n            setIsHovered(false);\n            if (mode === 'instant') {\n                setIsPressed(false);\n                const newState = eventManager.handleMouseUp();\n                setCurrentState(newState);\n                onStateChange === null || onStateChange === void 0 ? void 0 : onStateChange(newState);\n            }\n        }\n    }[\"SecondaryButton.useCallback[handleMouseLeave]\"], [\n        disabled,\n        mode,\n        eventManager,\n        onStateChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: getCurrentStyles(),\n        onClick: handleClick,\n        onMouseDown: handleMouseDown,\n        onMouseUp: handleMouseUp,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        disabled: disabled,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            style: getTextStyles(),\n            children: text\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecondaryButton, \"Rg5qdhR5Aw6tHJ3e2U44sHHiZP0=\");\n_c = SecondaryButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SecondaryButton);\nvar _c;\n$RefreshReg$(_c, \"SecondaryButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts":
/*!*****************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/event/event_secondary.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstantModeHandler: () => (/* binding */ InstantModeHandler),\n/* harmony export */   SecondaryButtonEventManager: () => (/* binding */ SecondaryButtonEventManager),\n/* harmony export */   ToggleModeHandler: () => (/* binding */ ToggleModeHandler)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_define_property.js\");\n// 普通按键事件处理\n// 按键模式类型\n\n// 持续状态模式事件处理\nclass ToggleModeHandler {\n    // 切换状态\n    toggle() {\n        this.isActive = !this.isActive;\n        return this.isActive;\n    }\n    // 获取当前状态\n    getState() {\n        return this.isActive;\n    }\n    // 设置状态\n    setState(state) {\n        this.isActive = state;\n    }\n    constructor(initialState = false){\n        (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_0__._)(this, \"isActive\", false);\n        this.isActive = initialState;\n    }\n}\n// 瞬时状态模式事件处理\nclass InstantModeHandler {\n    // 按下事件\n    onMouseDown() {\n        this.isPressed = true;\n        return this.isPressed;\n    }\n    // 松开事件\n    onMouseUp() {\n        this.isPressed = false;\n        return this.isPressed;\n    }\n    // 获取当前按压状态\n    getState() {\n        return this.isPressed;\n    }\n    constructor(){\n        (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_0__._)(this, \"isPressed\", false);\n    }\n}\n// 按键事件管理器\nclass SecondaryButtonEventManager {\n    // 处理点击事件\n    handleClick() {\n        if (this.mode === 'toggle') {\n            return this.toggleHandler.toggle();\n        }\n        return false;\n    }\n    // 处理鼠标按下事件\n    handleMouseDown() {\n        if (this.mode === 'instant') {\n            return this.instantHandler.onMouseDown();\n        }\n        return this.getState();\n    }\n    // 处理鼠标松开事件\n    handleMouseUp() {\n        if (this.mode === 'instant') {\n            return this.instantHandler.onMouseUp();\n        }\n        return this.getState();\n    }\n    // 获取当前状态\n    getState() {\n        if (this.mode === 'toggle') {\n            return this.toggleHandler.getState();\n        } else {\n            return this.instantHandler.getState();\n        }\n    }\n    // 设置模式\n    setMode(mode) {\n        this.mode = mode;\n    }\n    // 获取模式\n    getMode() {\n        return this.mode;\n    }\n    // 设置状态（仅对toggle模式有效）\n    setState(state) {\n        if (this.mode === 'toggle') {\n            this.toggleHandler.setState(state);\n        }\n    }\n    constructor(mode = 'toggle', initialState = false){\n        (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_0__._)(this, \"mode\", void 0);\n        (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_0__._)(this, \"toggleHandler\", void 0);\n        (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_0__._)(this, \"instantHandler\", void 0);\n        this.mode = mode;\n        this.toggleHandler = new ToggleModeHandler(initialState);\n        this.instantHandler = new InstantModeHandler();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts":
/*!*****************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/style/style_secondary.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   secondaryButtonStyles: () => (/* binding */ secondaryButtonStyles)\n/* harmony export */ });\n// 普通按键样式配置\nconst secondaryButtonStyles = {\n    // 默认样式\n    default: {\n        // 按键样式\n        button: {\n            name: 'secondary',\n            shape: 'rectangle',\n            height: '50px',\n            width: '200px',\n            borderRadius: '5px',\n            backgroundColor: '#f1f1f1',\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            cursor: 'pointer',\n            border: 'none',\n            outline: 'none'\n        },\n        // 文字样式\n        text: {\n            defaultText: '按键',\n            fontSize: '20px',\n            color: '#242424',\n            textAlign: 'center',\n            lineHeight: '1.5'\n        }\n    },\n    // 持续状态模式样式\n    toggle: {\n        // 激活状态\n        active: {\n            backgroundColor: '#929292',\n            hover: {\n                backgroundColor: '#858585'\n            }\n        },\n        // 未激活状态\n        inactive: {\n            backgroundColor: '#f1f1f1',\n            hover: {\n                backgroundColor: '#e4e4e4'\n            }\n        }\n    },\n    // 瞬时状态模式样式\n    instant: {\n        // 默认状态\n        default: {\n            backgroundColor: '#f1f1f1'\n        },\n        // 鼠标悬停\n        hover: {\n            backgroundColor: '#e4e4e4'\n        },\n        // 点击按下\n        active: {\n            backgroundColor: '#858585'\n        },\n        // 松开回弹\n        release: {\n            backgroundColor: '#f1f1f1'\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/Store/store.ts":
/*!*********************************!*\
  !*** ./frontend/Store/store.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n// 状态管理文件\n\nconst useStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        // 默认状态：模式按键为激活状态\n        activeButton: 'mode',\n        setActiveButton: (button)=>set({\n                activeButton: button\n            })\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL1N0b3JlL3N0b3JlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsU0FBUztBQUN3QjtBQVExQixNQUFNQyxXQUFXRCwrQ0FBTUEsQ0FBVyxDQUFDRSxNQUFTO1FBQ2pELGlCQUFpQjtRQUNqQkMsY0FBYztRQUNkQyxpQkFBaUIsQ0FBQ0MsU0FBV0gsSUFBSTtnQkFBRUMsY0FBY0U7WUFBTztJQUMxRCxJQUFJIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvU3RvcmUvc3RvcmUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g54q25oCB566h55CG5paH5Lu2XG5pbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcblxuZXhwb3J0IGludGVyZmFjZSBBcHBTdGF0ZSB7XG4gIC8vIOaMiemUrueKtuaAgeeuoeeQhlxuICBhY3RpdmVCdXR0b246ICdtb2RlJyB8ICdidXNpbmVzcyc7XG4gIHNldEFjdGl2ZUJ1dHRvbjogKGJ1dHRvbjogJ21vZGUnIHwgJ2J1c2luZXNzJykgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGNvbnN0IHVzZVN0b3JlID0gY3JlYXRlPEFwcFN0YXRlPigoc2V0KSA9PiAoe1xuICAvLyDpu5jorqTnirbmgIHvvJrmqKHlvI/mjInplK7kuLrmv4DmtLvnirbmgIFcbiAgYWN0aXZlQnV0dG9uOiAnbW9kZScsXG4gIHNldEFjdGl2ZUJ1dHRvbjogKGJ1dHRvbikgPT4gc2V0KHsgYWN0aXZlQnV0dG9uOiBidXR0b24gfSksXG59KSk7XG4iXSwibmFtZXMiOlsiY3JlYXRlIiwidXNlU3RvcmUiLCJzZXQiLCJhY3RpdmVCdXR0b24iLCJzZXRBY3RpdmVCdXR0b24iLCJidXR0b24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/Store/store.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/app/main_container.tsx":
/*!*****************************************!*\
  !*** ./frontend/app/main_container.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _componets_componet_componetA1__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../componets/componet/componetA1 */ \"(app-pages-browser)/./frontend/componets/componet/componetA1.tsx\");\n/* harmony import */ var _componets_interaction_compnet_interactionB__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../componets/interaction/compnet_interactionB */ \"(app-pages-browser)/./frontend/componets/interaction/compnet_interactionB.tsx\");\n// 一级容器文件\n\n\n\n\nconst MainContainer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: '100vh',\n            width: '100vw',\n            backgroundColor: '#242424',\n            display: 'flex',\n            flexDirection: 'row',\n            justifyContent: 'center',\n            alignItems: 'center',\n            overflow: 'hidden'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componets_componet_componetA1__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/app/main_container.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componets_interaction_compnet_interactionB__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/app/main_container.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/app/main_container.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MainContainer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainContainer);\nvar _c;\n$RefreshReg$(_c, \"MainContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL2FwcC9tYWluX2NvbnRhaW5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUEsU0FBUzs7QUFDaUI7QUFDZ0M7QUFDdUI7QUFFakYsTUFBTUcsZ0JBQTBCO0lBQzlCLHFCQUNFLDhEQUFDQztRQUFJQyxPQUFPO1lBQ1ZDLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxpQkFBaUI7WUFDakJDLFNBQVM7WUFDVEMsZUFBZTtZQUNmQyxnQkFBZ0I7WUFDaEJDLFlBQVk7WUFDWkMsVUFBVTtRQUNaOzswQkFDRSw4REFBQ1osc0VBQVVBOzs7OzswQkFDWCw4REFBQ0MsbUZBQW9CQTs7Ozs7Ozs7Ozs7QUFHM0I7S0FoQk1DO0FBa0JOLGlFQUFlQSxhQUFhQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvYXBwL21haW5fY29udGFpbmVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDkuIDnuqflrrnlmajmlofku7ZcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgQ29tcG9uZXRBMSBmcm9tICcuLi9jb21wb25ldHMvY29tcG9uZXQvY29tcG9uZXRBMSc7XG5pbXBvcnQgQ29tcG9uZXRJbnRlcmFjdGlvbkIgZnJvbSAnLi4vY29tcG9uZXRzL2ludGVyYWN0aW9uL2NvbXBuZXRfaW50ZXJhY3Rpb25CJztcblxuY29uc3QgTWFpbkNvbnRhaW5lcjogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBzdHlsZT17e1xuICAgICAgaGVpZ2h0OiAnMTAwdmgnLFxuICAgICAgd2lkdGg6ICcxMDB2dycsXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMjQyNDI0JyxcbiAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgIGZsZXhEaXJlY3Rpb246ICdyb3cnLFxuICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICBvdmVyZmxvdzogJ2hpZGRlbidcbiAgICB9fT5cbiAgICAgIDxDb21wb25ldEExIC8+XG4gICAgICA8Q29tcG9uZXRJbnRlcmFjdGlvbkIgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IE1haW5Db250YWluZXI7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb21wb25ldEExIiwiQ29tcG9uZXRJbnRlcmFjdGlvbkIiLCJNYWluQ29udGFpbmVyIiwiZGl2Iiwic3R5bGUiLCJoZWlnaHQiLCJ3aWR0aCIsImJhY2tncm91bmRDb2xvciIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwianVzdGlmeUNvbnRlbnQiLCJhbGlnbkl0ZW1zIiwib3ZlcmZsb3ciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/app/main_container.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/componet/componetA1.tsx":
/*!****************************************************!*\
  !*** ./frontend/componets/componet/componetA1.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// 二级容器文件A1\n\n\nconst ComponetA1 = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: '95vh',\n            width: '95vh',\n            backgroundColor: '#6d6d6d',\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: 'center',\n            overflow: 'hidden'\n        }\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetA1.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ComponetA1;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetA1);\nvar _c;\n$RefreshReg$(_c, \"ComponetA1\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL2NvbXBvbmV0cy9jb21wb25ldC9jb21wb25ldEExLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsV0FBVzs7QUFDZTtBQUUxQixNQUFNQyxhQUF1QjtJQUMzQixxQkFDRSw4REFBQ0M7UUFBSUMsT0FBTztZQUNWQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsaUJBQWlCO1lBQ2pCQyxTQUFTO1lBQ1RDLGVBQWU7WUFDZkMsZ0JBQWdCO1lBQ2hCQyxZQUFZO1lBQ1pDLFVBQVU7UUFDWjs7Ozs7O0FBSUo7S0FmTVY7QUFpQk4saUVBQWVBLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9mcm9udGVuZC9jb21wb25ldHMvY29tcG9uZXQvY29tcG9uZXRBMS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy8g5LqM57qn5a655Zmo5paH5Lu2QTFcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IENvbXBvbmV0QTE6IFJlYWN0LkZDID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgIGhlaWdodDogJzk1dmgnLFxuICAgICAgd2lkdGg6ICc5NXZoJyxcbiAgICAgIGJhY2tncm91bmRDb2xvcjogJyM2ZDZkNmQnLFxuICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsXG4gICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgIG92ZXJmbG93OiAnaGlkZGVuJ1xuICAgIH19PlxuICAgICAgey8qIOe7hOS7tkEx5YaF5a65ICovfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ29tcG9uZXRBMTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNvbXBvbmV0QTEiLCJkaXYiLCJzdHlsZSIsImhlaWdodCIsIndpZHRoIiwiYmFja2dyb3VuZENvbG9yIiwiZGlzcGxheSIsImZsZXhEaXJlY3Rpb24iLCJqdXN0aWZ5Q29udGVudCIsImFsaWduSXRlbXMiLCJvdmVyZmxvdyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/componet/componetA1.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/componet/componetB1.tsx":
/*!****************************************************!*\
  !*** ./frontend/componets/componet/componetB1.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// 二级容器文件B1\n\n\nconst ComponetB1 = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            width: '100%',\n            height: '100%',\n            backgroundColor: '#6d6d6d',\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: 'center',\n            overflow: 'hidden'\n        }\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetB1.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ComponetB1;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetB1);\nvar _c;\n$RefreshReg$(_c, \"ComponetB1\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL2NvbXBvbmV0cy9jb21wb25ldC9jb21wb25ldEIxLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsV0FBVzs7QUFDZTtBQUUxQixNQUFNQyxhQUF1QjtJQUMzQixxQkFDRSw4REFBQ0M7UUFBSUMsT0FBTztZQUNWQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsaUJBQWlCO1lBQ2pCQyxTQUFTO1lBQ1RDLGVBQWU7WUFDZkMsZ0JBQWdCO1lBQ2hCQyxZQUFZO1lBQ1pDLFVBQVU7UUFDWjs7Ozs7O0FBSUo7S0FmTVY7QUFpQk4saUVBQWVBLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9mcm9udGVuZC9jb21wb25ldHMvY29tcG9uZXQvY29tcG9uZXRCMS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy8g5LqM57qn5a655Zmo5paH5Lu2QjFcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IENvbXBvbmV0QjE6IFJlYWN0LkZDID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgIHdpZHRoOiAnMTAwJScsXG4gICAgICBoZWlnaHQ6ICcxMDAlJyxcbiAgICAgIGJhY2tncm91bmRDb2xvcjogJyM2ZDZkNmQnLFxuICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsXG4gICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgIG92ZXJmbG93OiAnaGlkZGVuJ1xuICAgIH19PlxuICAgICAgey8qIOe7hOS7tkIx5YaF5a65ICovfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ29tcG9uZXRCMTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNvbXBvbmV0QjEiLCJkaXYiLCJzdHlsZSIsIndpZHRoIiwiaGVpZ2h0IiwiYmFja2dyb3VuZENvbG9yIiwiZGlzcGxheSIsImZsZXhEaXJlY3Rpb24iLCJqdXN0aWZ5Q29udGVudCIsImFsaWduSXRlbXMiLCJvdmVyZmxvdyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/componet/componetB1.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/componet/componetB2.tsx":
/*!****************************************************!*\
  !*** ./frontend/componets/componet/componetB2.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// 二级容器文件B2\n\n\nconst ComponetB2 = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            width: '100%',\n            height: '100%',\n            backgroundColor: '#b6b6b6',\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: 'center',\n            overflow: 'hidden'\n        }\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetB2.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ComponetB2;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetB2);\nvar _c;\n$RefreshReg$(_c, \"ComponetB2\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL2NvbXBvbmV0cy9jb21wb25ldC9jb21wb25ldEIyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsV0FBVzs7QUFDZTtBQUUxQixNQUFNQyxhQUF1QjtJQUMzQixxQkFDRSw4REFBQ0M7UUFBSUMsT0FBTztZQUNWQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsaUJBQWlCO1lBQ2pCQyxTQUFTO1lBQ1RDLGVBQWU7WUFDZkMsZ0JBQWdCO1lBQ2hCQyxZQUFZO1lBQ1pDLFVBQVU7UUFDWjs7Ozs7O0FBSUo7S0FmTVY7QUFpQk4saUVBQWVBLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9mcm9udGVuZC9jb21wb25ldHMvY29tcG9uZXQvY29tcG9uZXRCMi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy8g5LqM57qn5a655Zmo5paH5Lu2QjJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IENvbXBvbmV0QjI6IFJlYWN0LkZDID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgIHdpZHRoOiAnMTAwJScsXG4gICAgICBoZWlnaHQ6ICcxMDAlJyxcbiAgICAgIGJhY2tncm91bmRDb2xvcjogJyNiNmI2YjYnLFxuICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsXG4gICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgIG92ZXJmbG93OiAnaGlkZGVuJ1xuICAgIH19PlxuICAgICAgey8qIOe7hOS7tkIy5YaF5a65ICovfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ29tcG9uZXRCMjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNvbXBvbmV0QjIiLCJkaXYiLCJzdHlsZSIsIndpZHRoIiwiaGVpZ2h0IiwiYmFja2dyb3VuZENvbG9yIiwiZGlzcGxheSIsImZsZXhEaXJlY3Rpb24iLCJqdXN0aWZ5Q29udGVudCIsImFsaWduSXRlbXMiLCJvdmVyZmxvdyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/componet/componetB2.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/componet/componetButton.tsx":
/*!********************************************************!*\
  !*** ./frontend/componets/componet/componetButton.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ButtonCodes/secondary/SecondaryButton/SecondaryButton */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\");\n/* harmony import */ var _Store_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Store/store */ \"(app-pages-browser)/./frontend/Store/store.ts\");\n// 二级容器文件Button\n\nvar _s = $RefreshSig$();\n\n\n\nconst ComponetButton = ()=>{\n    _s();\n    const { activeButton, setActiveButton } = (0,_Store_store__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const handleModeClick = ()=>{\n        setActiveButton('mode');\n    };\n    const handleBusinessClick = ()=>{\n        setActiveButton('business');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: '3vh',\n            width: '20vw',\n            backgroundColor: 'transparent',\n            display: 'flex',\n            flexDirection: 'row',\n            justifyContent: 'center',\n            alignItems: 'center',\n            overflow: 'hidden',\n            position: 'absolute',\n            top: '0',\n            left: '0'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                text: \"模式\",\n                mode: \"toggle\",\n                isActive: activeButton === 'mode',\n                onClick: handleModeClick,\n                customStyles: {\n                    height: '100%',\n                    width: '50%',\n                    fontSize: 'clamp(8px, 1.5vw, 16px)'\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetButton.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                text: \"业务\",\n                mode: \"toggle\",\n                isActive: activeButton === 'business',\n                onClick: handleBusinessClick,\n                customStyles: {\n                    height: '100%',\n                    width: '50%',\n                    fontSize: 'clamp(8px, 1.5vw, 16px)'\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetButton.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetButton.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ComponetButton, \"jOwpuz9v/Cu30ApMtpbRft+OiUw=\", false, function() {\n    return [\n        _Store_store__WEBPACK_IMPORTED_MODULE_3__.useStore\n    ];\n});\n_c = ComponetButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetButton);\nvar _c;\n$RefreshReg$(_c, \"ComponetButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/componet/componetButton.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/interaction/compnet_interactionB.tsx":
/*!*****************************************************************!*\
  !*** ./frontend/componets/interaction/compnet_interactionB.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Store_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Store/store */ \"(app-pages-browser)/./frontend/Store/store.ts\");\n/* harmony import */ var _componet_componetB1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../componet/componetB1 */ \"(app-pages-browser)/./frontend/componets/componet/componetB1.tsx\");\n/* harmony import */ var _componet_componetB2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../componet/componetB2 */ \"(app-pages-browser)/./frontend/componets/componet/componetB2.tsx\");\n/* harmony import */ var _componet_componetButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../componet/componetButton */ \"(app-pages-browser)/./frontend/componets/componet/componetButton.tsx\");\n// 容器交互文件\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CompnetInteractionB = ()=>{\n    _s();\n    const { activeButton } = (0,_Store_store__WEBPACK_IMPORTED_MODULE_2__.useStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: '95vh',\n            width: '20vw',\n            marginLeft: '1vw',\n            position: 'relative'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componet_componetButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/compnet_interactionB.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            activeButton === 'mode' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componet_componetB1__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/compnet_interactionB.tsx\",\n                lineNumber: 22,\n                columnNumber: 35\n            }, undefined),\n            activeButton === 'business' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componet_componetB2__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/compnet_interactionB.tsx\",\n                lineNumber: 23,\n                columnNumber: 39\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/compnet_interactionB.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CompnetInteractionB, \"Lv/oU7EQ7O/eBfr28V3pgIel2/w=\", false, function() {\n    return [\n        _Store_store__WEBPACK_IMPORTED_MODULE_2__.useStore\n    ];\n});\n_c = CompnetInteractionB;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompnetInteractionB);\nvar _c;\n$RefreshReg$(_c, \"CompnetInteractionB\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/interaction/compnet_interactionB.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@swc/helpers/esm/_define_property.js":
/*!***********************************************************!*\
  !*** ./node_modules/@swc/helpers/esm/_define_property.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: () => (/* binding */ _define_property)\n/* harmony export */ });\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });\n    } else obj[key] = value;\n\n    return obj;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvZXNtL19kZWZpbmVfcHJvcGVydHkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQSwwQ0FBMEMsb0VBQW9FO0FBQzlHLE1BQU07O0FBRU47QUFDQTtBQUNpQyIsInNvdXJjZXMiOlsiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvZXNtL19kZWZpbmVfcHJvcGVydHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gX2RlZmluZV9wcm9wZXJ0eShvYmosIGtleSwgdmFsdWUpIHtcbiAgICBpZiAoa2V5IGluIG9iaikge1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqLCBrZXksIHsgdmFsdWU6IHZhbHVlLCBlbnVtZXJhYmxlOiB0cnVlLCBjb25maWd1cmFibGU6IHRydWUsIHdyaXRhYmxlOiB0cnVlIH0pO1xuICAgIH0gZWxzZSBvYmpba2V5XSA9IHZhbHVlO1xuXG4gICAgcmV0dXJuIG9iajtcbn1cbmV4cG9ydCB7IF9kZWZpbmVfcHJvcGVydHkgYXMgXyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@swc/helpers/esm/_define_property.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(app-pages-browser)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ6aGFvX3ppX2ZlbmclMkZEZXNrdG9wJTJGJUU0JUJCJUEzJUU3JUEwJTgxJUU2JTk2JTg3JUU0JUJCJUI2JTJGTHlyaWNXcml0aW5nUHJvamVjdCUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0pBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/zustand/esm/react.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/react.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/vanilla */ \"(app-pages-browser)/./node_modules/zustand/esm/vanilla.mjs\");\n\n\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS9yZWFjdC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEwQjtBQUNvQjs7QUFFOUM7QUFDQTtBQUNBLGdCQUFnQix1REFBMEI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLGdEQUFtQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxjQUFjLDREQUFXO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3Qvbm9kZV9tb2R1bGVzL3p1c3RhbmQvZXNtL3JlYWN0Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY3JlYXRlU3RvcmUgfSBmcm9tICd6dXN0YW5kL3ZhbmlsbGEnO1xuXG5jb25zdCBpZGVudGl0eSA9IChhcmcpID0+IGFyZztcbmZ1bmN0aW9uIHVzZVN0b3JlKGFwaSwgc2VsZWN0b3IgPSBpZGVudGl0eSkge1xuICBjb25zdCBzbGljZSA9IFJlYWN0LnVzZVN5bmNFeHRlcm5hbFN0b3JlKFxuICAgIGFwaS5zdWJzY3JpYmUsXG4gICAgKCkgPT4gc2VsZWN0b3IoYXBpLmdldFN0YXRlKCkpLFxuICAgICgpID0+IHNlbGVjdG9yKGFwaS5nZXRJbml0aWFsU3RhdGUoKSlcbiAgKTtcbiAgUmVhY3QudXNlRGVidWdWYWx1ZShzbGljZSk7XG4gIHJldHVybiBzbGljZTtcbn1cbmNvbnN0IGNyZWF0ZUltcGwgPSAoY3JlYXRlU3RhdGUpID0+IHtcbiAgY29uc3QgYXBpID0gY3JlYXRlU3RvcmUoY3JlYXRlU3RhdGUpO1xuICBjb25zdCB1c2VCb3VuZFN0b3JlID0gKHNlbGVjdG9yKSA9PiB1c2VTdG9yZShhcGksIHNlbGVjdG9yKTtcbiAgT2JqZWN0LmFzc2lnbih1c2VCb3VuZFN0b3JlLCBhcGkpO1xuICByZXR1cm4gdXNlQm91bmRTdG9yZTtcbn07XG5jb25zdCBjcmVhdGUgPSAoY3JlYXRlU3RhdGUpID0+IGNyZWF0ZVN0YXRlID8gY3JlYXRlSW1wbChjcmVhdGVTdGF0ZSkgOiBjcmVhdGVJbXBsO1xuXG5leHBvcnQgeyBjcmVhdGUsIHVzZVN0b3JlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/zustand/esm/react.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore)\n/* harmony export */ });\nconst createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS92YW5pbGxhLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4SEFBOEg7QUFDOUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBOztBQUV1QiIsInNvdXJjZXMiOlsiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS92YW5pbGxhLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjcmVhdGVTdG9yZUltcGwgPSAoY3JlYXRlU3RhdGUpID0+IHtcbiAgbGV0IHN0YXRlO1xuICBjb25zdCBsaXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICBjb25zdCBzZXRTdGF0ZSA9IChwYXJ0aWFsLCByZXBsYWNlKSA9PiB7XG4gICAgY29uc3QgbmV4dFN0YXRlID0gdHlwZW9mIHBhcnRpYWwgPT09IFwiZnVuY3Rpb25cIiA/IHBhcnRpYWwoc3RhdGUpIDogcGFydGlhbDtcbiAgICBpZiAoIU9iamVjdC5pcyhuZXh0U3RhdGUsIHN0YXRlKSkge1xuICAgICAgY29uc3QgcHJldmlvdXNTdGF0ZSA9IHN0YXRlO1xuICAgICAgc3RhdGUgPSAocmVwbGFjZSAhPSBudWxsID8gcmVwbGFjZSA6IHR5cGVvZiBuZXh0U3RhdGUgIT09IFwib2JqZWN0XCIgfHwgbmV4dFN0YXRlID09PSBudWxsKSA/IG5leHRTdGF0ZSA6IE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLCBuZXh0U3RhdGUpO1xuICAgICAgbGlzdGVuZXJzLmZvckVhY2goKGxpc3RlbmVyKSA9PiBsaXN0ZW5lcihzdGF0ZSwgcHJldmlvdXNTdGF0ZSkpO1xuICAgIH1cbiAgfTtcbiAgY29uc3QgZ2V0U3RhdGUgPSAoKSA9PiBzdGF0ZTtcbiAgY29uc3QgZ2V0SW5pdGlhbFN0YXRlID0gKCkgPT4gaW5pdGlhbFN0YXRlO1xuICBjb25zdCBzdWJzY3JpYmUgPSAobGlzdGVuZXIpID0+IHtcbiAgICBsaXN0ZW5lcnMuYWRkKGxpc3RlbmVyKTtcbiAgICByZXR1cm4gKCkgPT4gbGlzdGVuZXJzLmRlbGV0ZShsaXN0ZW5lcik7XG4gIH07XG4gIGNvbnN0IGFwaSA9IHsgc2V0U3RhdGUsIGdldFN0YXRlLCBnZXRJbml0aWFsU3RhdGUsIHN1YnNjcmliZSB9O1xuICBjb25zdCBpbml0aWFsU3RhdGUgPSBzdGF0ZSA9IGNyZWF0ZVN0YXRlKHNldFN0YXRlLCBnZXRTdGF0ZSwgYXBpKTtcbiAgcmV0dXJuIGFwaTtcbn07XG5jb25zdCBjcmVhdGVTdG9yZSA9IChjcmVhdGVTdGF0ZSkgPT4gY3JlYXRlU3RhdGUgPyBjcmVhdGVTdG9yZUltcGwoY3JlYXRlU3RhdGUpIDogY3JlYXRlU3RvcmVJbXBsO1xuXG5leHBvcnQgeyBjcmVhdGVTdG9yZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/zustand/esm/vanilla.mjs\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);