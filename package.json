{"name": "lyricwritingproject", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"doc": "docs"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "react": "^19.1.1", "react-dom": "^19.1.1", "typescript": "^5.8.3"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@storybook/addon-a11y": "^9.0.18", "@storybook/addon-docs": "^9.0.18", "@storybook/addon-onboarding": "^9.0.18", "@storybook/addon-vitest": "^9.0.18", "@storybook/react-vite": "^9.0.18", "storybook": "^9.0.18", "vitest": "^3.2.4", "@vitest/browser": "^3.2.4", "playwright": "^1.54.1", "@vitest/coverage-v8": "^3.2.4"}}